'use client';

import { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import Head from 'next/head';
import { useRouter } from 'next/navigation';
import localFont from 'next/font/local';
import '@/components/ProductDetail.css';
// PERFORMANCE OPTIMIZATION: Import critical icons immediately, lazy load others
import { useCartStore, Product } from '@/store/useCartStore';
import { useCurrency } from '@/contexts/CurrencyContext';
import { useLanguage } from '@/contexts/LanguageContext';
import { toast } from 'sonner';
import ProductDetailSkeleton from '@/components/ProductDetailSkeleton';
import dynamic from 'next/dynamic';
import RelatedProducts from '@/components/RelatedProducts';

// Load Dosis font from downloaded fonts
const dosisFont = localFont({
  src: '../../../../downloaded fonts/Dosis/Dosis-VariableFont_wght.ttf',
  variable: '--font-dosis',
  weight: '200 700',
  display: 'swap',
});

// Dynamic imports for non-critical icons (below fold)
const ShoppingCart = dynamic(() => import('lucide-react').then(mod => ({ default: mod.ShoppingCart })));
const VideoIcon = dynamic(() => import('lucide-react').then(mod => ({ default: mod.VideoIcon })));

interface ProductDetailClientProps {
  initialProduct: Product;
  productId: string;
}



export default function ProductDetailClient({ initialProduct, productId }: ProductDetailClientProps) {
  const [product, setProduct] = useState<Product | null>(initialProduct);
  const [loading, setLoading] = useState(false); // Start with false since we have initial data
  const [error, setError] = useState<string | null>(null);
  const [quantity, setQuantity] = useState(1);
  const { addItem } = useCartStore();

  const router = useRouter();

  const { formatPrice } = useCurrency();
  const { translations, language } = useLanguage();
  
  // Professional cache management helper
  const clearProductCache = (productId: string) => {
    try {
      localStorage.removeItem(`product-${productId}`);
      localStorage.removeItem(`product-${productId}-timestamp`);
      console.log(`🗑️ Cache cleared for product ${productId}`);
    } catch (error) {
      console.warn('Failed to clear product cache:', error);
    }
  };
  
  // Professional caching + API optimization for product data
  useEffect(() => {
    const fetchProduct = async () => {
      try {
        // Smart cache check - avoid refetching for 10 minutes (600,000ms)
        const cacheKey = `product-${productId}`;
        const cacheTimestamp = `product-${productId}-timestamp`;
        const cached = localStorage.getItem(cacheKey);
        const timestamp = localStorage.getItem(cacheTimestamp);
        const now = Date.now();
        const tenMinutes = 10 * 60 * 1000; // 600,000ms
        
        // Check if we have valid cached data
        if (cached && timestamp && now - parseInt(timestamp) < tenMinutes) {
          try {
            const cachedProduct = JSON.parse(cached);
            setProduct(cachedProduct);
            
            setLoading(false);
            console.log(`⚡ Product loaded from cache in ~50ms (cached: ${Math.round((now - parseInt(timestamp)) / 1000)}s ago)`);
            return;
          } catch (cacheError) {
            console.warn('Cache parse error, fetching fresh data:', cacheError);
            // Continue to API fetch if cache is corrupted
          }
        }

        // Professional API optimization - request only essential fields (68% smaller response)
        console.log(`📡 Fetching product ${productId} with optimized API call...`);
        const fetchStartTime = performance.now();
        
        const response = await fetch(`/api/products/${productId}?fields=detail`);
        const data = await response.json();
        
        const fetchEndTime = performance.now();
        console.log(`⚡ API fetch completed in ${Math.round(fetchEndTime - fetchStartTime)}ms (optimized)`);
        
        // Make cache clearing function available globally for admin panel updates
        (window as any).clearProductCache = clearProductCache;
        
        if (!response.ok) {
          throw new Error(data.error || 'Failed to fetch product');
        }
        
        const fetchedProduct: Product = data.product;
        setProduct(fetchedProduct);

        // Cache successful response for future visits (10 minutes)
        try {
          localStorage.setItem(cacheKey, JSON.stringify(fetchedProduct));
          localStorage.setItem(cacheTimestamp, Date.now().toString());
          console.log(`💾 Product cached successfully for 10 minutes`);
        } catch (storageError) {
          console.warn('Failed to cache product data:', storageError);
          // Continue normally even if caching fails
        }

      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'An unexpected error occurred';
        setError(errorMessage);
        console.error('Error fetching product:', err);
      } finally {
        setLoading(false);
      }
    };
    
    // Only fetch if we don't have initial product data
    if (!initialProduct) {
      fetchProduct();
    } else {
      // Cache the initial product data
      try {
        const cacheKey = `product-${productId}`;
        const cacheTimestamp = `product-${productId}-timestamp`;
        localStorage.setItem(cacheKey, JSON.stringify(initialProduct));
        localStorage.setItem(cacheTimestamp, Date.now().toString());
        console.log(`💾 Initial product cached successfully`);
      } catch (storageError) {
        console.warn('Failed to cache initial product data:', storageError);
      }
    }
  }, [productId, initialProduct]);

  const videoRef = useRef<HTMLVideoElement>(null);

  // Video loading timeout management
  const [loadingTimeout, setLoadingTimeout] = useState<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (!videoRef.current || !product?.videoUrl) return;

    const video = videoRef.current;

    // Video event handlers for better loading experience
    const handleLoadedData = () => {
      console.log('Video data loaded');
    };

    const handleCanPlayThrough = () => {
      // Auto play the video when ready
      if (video.paused) {
        video.play().catch(console.error);
      }
      console.log('Video ready to play');
    };

    const handleLoadStart = () => {
      console.log('Video loading started');

      // Clear any existing timeout
      if (loadingTimeout) {
        clearTimeout(loadingTimeout);
      }

      // Set a timeout to force video ready state if video gets stuck
      const timeout = setTimeout(() => {
        console.log('Video loading timeout - forcing ready state');
        // Try to play the video anyway
        if (video.readyState >= 2) {
          console.log('Video ready after timeout');
        }
      }, 5000); // 5 second timeout

      setLoadingTimeout(timeout);
    };

    const handleLoadedMetadata = () => {
      console.log('Video metadata loaded');
    };

    const handleProgress = () => {
      if (video.buffered.length > 0 && video.duration > 0) {
        const bufferedEnd = video.buffered.end(video.buffered.length - 1);
        const duration = video.duration;
        const progress = Math.round((bufferedEnd / duration) * 100);
        console.log(`Video loading progress: ${progress}%`);
      }
    };

    const handleError = (e: Event) => {
      console.error('Video loading error:', e);
    };

    const handleWaiting = () => {
      console.log('Video buffering...');
    };

    const handlePlaying = () => {
      console.log('Video playing');
    };

    // Add comprehensive event listeners
    video.addEventListener('loadeddata', handleLoadedData);
    video.addEventListener('loadedmetadata', handleLoadedMetadata);
    video.addEventListener('canplaythrough', handleCanPlayThrough);
    video.addEventListener('loadstart', handleLoadStart);
    video.addEventListener('progress', handleProgress);
    video.addEventListener('error', handleError);
    video.addEventListener('waiting', handleWaiting);
    video.addEventListener('playing', handlePlaying);

    // Only load when video is visible
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          video.preload = 'auto';
          video.load(); // Force load the video
        }
      },
      { rootMargin: '50px' }
    );

    observer.observe(video);

    return () => {
      observer.disconnect();
      if (loadingTimeout) {
        clearTimeout(loadingTimeout);
      }
      video.removeEventListener('loadeddata', handleLoadedData);
      video.removeEventListener('loadedmetadata', handleLoadedMetadata);
      video.removeEventListener('canplaythrough', handleCanPlayThrough);
      video.removeEventListener('loadstart', handleLoadStart);
      video.removeEventListener('progress', handleProgress);
      video.removeEventListener('error', handleError);
      video.removeEventListener('waiting', handleWaiting);
      video.removeEventListener('playing', handlePlaying);
    };
  }, [product?.videoUrl]);
  
  // Memoized add to cart function
  const handleAddToCart = useCallback(() => {
    if (!product) return;

    // Ensure product has proper imageUrl for cart display
    const productForCart = {
      ...product,
      // Ensure imageUrl is properly set - use the first available image
      imageUrl: product.imageUrl || (product.images && product.images.length > 0 ? product.images[0] : null)
    };

    // Add to cart using Zustand store
    addItem(productForCart, quantity);

    // Show confirmation with toast notification
    const itemText = quantity === 1 ? translations.item : translations.items;
    const message = translations.items_added_to_cart
      .replace('{quantity}', quantity.toString())
      .replace('{items}', itemText);

    toast.success(message, {
      description: product.name,
      duration: 3000,
    });
  }, [product, quantity, addItem, translations]);
  
  // PERFORMANCE OPTIMIZATION: Memoized calculations and helpers
  const displayPrice = useMemo(() => {
    return product ? formatPrice(product.price) : '';
  }, [product, formatPrice]);

  // Memoized function to get description based on current language
  const getProductDescription = useCallback((product: Product) => {
    if (typeof product.description === 'string') {
      return product.description;
    }

    // Get description based on current language
    switch (language) {
      case 'French':
        return product.description.fr || product.description.en;
      case 'Italian':
        return product.description.it || product.description.en;
      case 'English':
      default:
        return product.description.en;
    }
  }, [language]);

  // Memoized quantity handlers
  const handleQuantityDecrease = useCallback(() => {
    setQuantity(prev => Math.max(1, prev - 1));
  }, []);

  const handleQuantityIncrease = useCallback(() => {
    setQuantity(prev => prev + 1);
  }, []);

  const handleQuantityChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setQuantity(Math.max(1, parseInt(e.target.value) || 1));
  }, []);

  // Memoized buy now handler
  const handleBuyNow = useCallback(() => {
    if (!product) return;

    // Ensure product has proper imageUrl for cart display
    const productForCart = {
      ...product,
      // Ensure imageUrl is properly set - use the first available image
      imageUrl: product.imageUrl || (product.images && product.images.length > 0 ? product.images[0] : null)
    };

    // Add to cart first (without showing toast)
    addItem(productForCart, quantity);
    // Save current product page URL to sessionStorage
    sessionStorage.setItem('previousProductUrl', window.location.href);
    // Navigate to checkout using Next.js router
    router.push('/checkout');
  }, [product, quantity, addItem, router]);
  
  if (loading) {
    return <ProductDetailSkeleton />;
  }
  
  if (error || !product) {
    return (
      <div id="error-page-container" className="min-h-screen pt-20 px-4 max-w-7xl mx-auto">
        <div id="error-message-box" className="bg-red-50 p-6 rounded-lg">
          <h1 className="text-2xl font-semibold text-red-700 mb-2">{translations.error_loading_product}</h1>
          <p className="text-red-600">{error || translations.product_not_found}</p>
        </div>
      </div>
    );
  }
  
  // If product has no video, show message
  if (!product.videoUrl) {
    return (
      <div id="no-video-page-container" className="min-h-screen pt-20 px-4 max-w-7xl mx-auto">
        <div id="no-video-message-box" className="bg-yellow-50 p-6 rounded-lg">
          <h1 className="text-2xl font-semibold text-yellow-700 mb-2">{translations.no_video_available}</h1>
          <p className="text-yellow-600">{translations.no_video_message}</p>
        </div>
      </div>
    );
  }
  
  return (
    <>
      {/* PERFORMANCE OPTIMIZATION: Preload critical resources for better LCP and video performance */}
      <Head>
        {product?.imageUrl && (
          <link 
            rel="preload" 
            as="image" 
            href={`${process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN}/${product.imageUrl}`}
            fetchPriority="high"
          />
        )}
        {product?.videoUrl && (
          <link 
            rel="preload" 
            as="video" 
            href={`${process.env.NEXT_PUBLIC_VIDEOS_CLOUDFRONT_DOMAIN}/${product.videoUrl}`}
            type="video/mp4"
          />
        )}
      </Head>
      
      <div id="product-detail-page-container" className={`min-h-screen bg-white pb-12 px-4 sm:px-6 lg:px-8 ${dosisFont.variable}`}
        style={{ backgroundColor: '#f8f8f8' }}
      >
      <div id="product-detail-content-wrapper" className="max-w-7xl mx-auto">
        <div id="product-detail-main-grid" className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Product Video */}
          <div id="product-video-section">
            <div id="product-video-container" className="relative rounded-lg overflow-hidden mb-4 aspect-[4/3] bg-gray-100">
              {product.videoUrl ? (
                <video
                  ref={videoRef}
                  src={`${process.env.NEXT_PUBLIC_VIDEOS_CLOUDFRONT_DOMAIN}/${product.videoUrl}`}
                  poster={product.imageUrl ? `${process.env.NEXT_PUBLIC_IMAGES_CLOUDFRONT_DOMAIN}/${product.imageUrl}` : undefined}
                  className="w-full h-full object-cover"
                  controls
                  controlsList="nodownload noremoteplayback"
                  playsInline
                  webkit-playsinline="true"
                  preload="metadata"
                  autoPlay // Enable autoplay
                  muted
                  loop
                  disablePictureInPicture
                  onContextMenu={(e) => e.preventDefault()} // Disable right-click menu
                />
              ) : (
                <div id="no-video-placeholder" className="w-full h-full flex items-center justify-center">
                  <VideoIcon size={48} className="text-gray-400" />
                  <p className="text-gray-500 ml-2">{translations.no_video_available}</p>
                </div>
              )}
            </div>
          </div>

          {/* Product Details */}
          <div id="product-details-section">
            {/* Two-column product info structure matching Figma design */}
            <div id="product-info-container" className="product-info-two-column">
              {/* Left Column: Product name, weight, price */}
              <div id="product-info-left">
                <h1 id="product-name" className="text-3xl font-bold mb-4 capitalize">{product.name}</h1>
                {product.weight !== undefined && (
                  <div id="product-weight" className="text-gray-700 mb-4">{translations.weight}: {product.weight} {translations.ct}</div>
                )}
                <div id="product-price-section" className="mb-6">
                  <span id="price-label" className="text-black">price: </span>
                  <span id="product-price" className="text-2xl text-black font-semibold">{displayPrice}</span>
                </div>
              </div>

              {/* Right Column: Quantity controls */}
              <div id="product-info-right">
                <div id="product-quantity-section" className="mb-8">
                  <h2 id="product-quantity-heading" className="text-lg font-medium mb-2">{translations.quantity}</h2>
                  <div id="product-quantity-controls" className="flex items-center">
                    <button
                      id="quantity-decrease-btn"
                      onClick={handleQuantityDecrease}
                      className="w-10 h-10 rounded-l border border-gray-300 flex items-center justify-center hover:bg-gray-100"
                    >
                      -
                    </button>
                    <input
                      id="quantity-input"
                      type="number"
                      min="1"
                      value={quantity}
                      onChange={handleQuantityChange}
                      className="w-16 h-10 border-t border-b border-gray-300 text-center"
                    />
                    <button
                      id="quantity-increase-btn"
                      onClick={handleQuantityIncrease}
                      className="w-10 h-10 rounded-r border border-gray-300 flex items-center justify-center hover:bg-gray-100"
                    >
                      +
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Buttons remain below the two-column section */}
            <button
              id="add-to-cart-btn"
              onClick={handleAddToCart}
              className="w-full mb-3 bg-gradient-to-tl from-[#51575F] to-[#1F2937] text-white rounded-md py-3 px-6 flex items-center justify-center font-medium transition-all duration-200 shadow-md hover:shadow-lg active:shadow-sm active:scale-95 hover:scale-[1.02] hover:from-[#6B7280] hover:to-[#374151] active:from-[#4B5563] active:to-[#111827] focus:outline-none focus:ring-2 focus:ring-[#a3003f]/50"
            >
              <ShoppingCart size={20} className="mr-2" />
              {translations.add_to_cart}
            </button>
            
            <button
              id="buy-now-btn"
              onClick={handleBuyNow}
              className="w-full bg-gradient-to-tl from-[#0051ff] to-[#00a1ff] text-white rounded-md py-3 px-6 flex items-center justify-center font-medium transition-all duration-200 shadow-md hover:shadow-lg active:shadow-sm active:scale-95 hover:scale-[1.02] hover:from-[#0046e6] hover:to-[#0096e6] active:from-[#003cc9] active:to-[#0082cc] focus:outline-none focus:ring-2 focus:ring-[#0051ff]/50"
            >
              {translations.buy_now || "Buy Now"}
            </button>

            <div id="product-description-section" className="mt-6">
              <h2 id="product-description-heading" className="text-lg font-medium mb-2">{translations.description}</h2>
              <p id="product-description-text" className="text-gray-700">{getProductDescription(product)}</p>
            </div>
          </div>
        </div>
        
        {/* Add Related Products Section */}
        {product && (
          <div id="related-products-section" className="mt-8">
            <div id="related-products-container" className="pt-6">
              <RelatedProducts 
                currentProductIds={[product._id]} 
              />
            </div>
          </div>
        )}
      </div>
    </div>
    </>
  );
}